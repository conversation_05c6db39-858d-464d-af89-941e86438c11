/**
 * 题目数据转换工具 - Admin App 专用
 * 提供前端题目数据与API数据格式之间的转换
 */

import { convertToGeneratedQuestion as baseConverter, type GeneratedQuestion } from '@sa/utils'

/**
 * 将前端题目数据转换为API所需的QuestionsApi.GeneratedQuestion格式
 * @param question 前端处理后的题目数据
 * @returns 转换后的QuestionsApi.GeneratedQuestion格式数据
 */
export function convertToApiGeneratedQuestion(question: Question.ProcessedQuestionData): QuestionsApi.GeneratedQuestion {
  // 使用基础转换器获取通用格式
  const baseResult = baseConverter(question)
  
  // 转换为API特定的类型格式
  const apiResult: QuestionsApi.GeneratedQuestion = {
    Analysis: baseResult.Analysis,
    Answer: baseResult.Answer,
    Chapters: baseResult.Chapters as QuestionsApi.QuestionChapter[] | null | undefined,
    KnowledgePoints: baseResult.KnowledgePoints as QuestionsA<PERSON>.QuestionKnowledgePoints[] | null | undefined,
    Options: baseResult.Options as QuestionsApi.QuestionOption[] | null | undefined,
    QuestionType: baseResult.QuestionType,
    QuestionTypeId: baseResult.QuestionTypeId,
    Title: baseResult.Title,
  }
  
  return apiResult
}

/**
 * 批量转换题目数据为API格式
 * @param questions 前端题目数据数组
 * @returns 转换后的QuestionsApi.GeneratedQuestion格式数据数组
 */
export function convertQuestionsToApiGeneratedQuestions(questions: Question.ProcessedQuestionData[]): QuestionsApi.GeneratedQuestion[] {
  if (!Array.isArray(questions)) {
    throw new Error('题目数据必须是数组格式')
  }

  return questions.map(question => convertToApiGeneratedQuestion(question))
}

/**
 * 为重新生成题目API准备请求参数
 * @param question 原始题目数据
 * @param options 重新生成选项
 * @returns 重新生成题目的请求参数
 */
export function prepareRegenerateQuestionRequest(
  question: Question.ProcessedQuestionData,
  options: {
    aiModelId: string
    difficultyLevelName?: string
    questionDirectionName?: string
    userRequirement?: string
  }
): QuestionsApi.RegenerateQuestionInputRequest {
  const originalQuestion = convertToApiGeneratedQuestion(question)
  
  return {
    AIModeId: options.aiModelId,
    DifficultyLevelName: options.difficultyLevelName || '',
    OriginalQuestion: originalQuestion,
    QuestionDirectionName: options.questionDirectionName || '',
    UserRequirement: options.userRequirement || '',
  }
}

/**
 * 为保存题目到题库API准备请求参数
 * @param questions 题目数据数组
 * @param options 保存选项
 * @returns 保存题目到题库的请求参数
 */
export function prepareSaveQuestionsToBankRequest(
  questions: Question.ProcessedQuestionData[],
  options: {
    chapterId: string
    difficultyId: string
    learningLevelId: string
  }
): QuestionsApi.SaveBatchQuestionsToBankInputRequest {
  const generatedQuestions = convertQuestionsToApiGeneratedQuestions(questions)
  
  return {
    ChapterId: options.chapterId,
    DifficultyId: options.difficultyId,
    LearningLevelId: options.learningLevelId,
    Questions: generatedQuestions,
  }
}
