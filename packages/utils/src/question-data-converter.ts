/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */
import { nanoid } from '@sa/utils'

// 本地类型定义，避免对外部API类型的依赖
export interface GeneratedQuestionOption {
  /** 选项内容 */
  Content: string
  /** 选项标识（A、B、C、D） */
  Option: string
}

export interface GeneratedQuestionKnowledgePoint {
  /** 知识点内容 */
  Content: string
  /** 知识点ID */
  Id: string
  /** 知识点层级 */
  Level: number
}

export interface GeneratedQuestionChapter {
  /** 章节名称 */
  ChapterName: string
  /** 章节ID */
  Id: string
}

export interface GeneratedQuestion {
  /** 答案解析 */
  Analysis: string
  /** 正确答案 */
  Answer: string
  /** 关联的章节列表（章节出题时使用） */
  Chapters?: GeneratedQuestionChapter[] | null
  /** 关联的知识点列表（知识点出题时使用） */
  KnowledgePoints?: GeneratedQuestionKnowledgePoint[] | null
  /** 选项（选择题、多项选择题、判断题需要） */
  Options?: GeneratedQuestionOption[] | null
  /** 题型 */
  QuestionType: string
  /** 题型ID */
  QuestionTypeId: string
  /** 题干 */
  Title: string
}

// // 定义流式数据的接口
// export interface StreamQuestionData {
//   Question: {
//     QuestionType: string
//     QuestionTypeId: string
//     Title: string
//     Options?: Array<{
//       Option: string
//       Content: string
//     }>
//     Answer: string
//     Analysis: string
//     KnowledgePoints: string[] | null
//   }
// }

// /**
//  * 处理后的题目数据结构
//  * 用于前端组件展示和交互的标准格式
//  */
// export interface ProcessedQuestionData {
//   /** 题目唯一标识符 */
//   id: string
//   /** 题型文本描述（如"单选题"、"多选题"等） */
//   typeText: string
//   /** 题型ID（对应后端定义的题型标识） */
//   typeId: string
//   /** 题目内容/题干 */
//   title: string
//   /** 对应前端组件名称（用于动态加载组件） */
//   componentsName: string
//   /**
//    * 题目选项（适用于选择题）
//    * @property label - 选项显示文本
//    * @property value - 选项实际值
//    */
//   options?: Array<{
//     label: string
//     value: string
//   }>
//   /** 正确答案（格式根据题型不同而变化） */
//   correctAnswer: string
//   /**
//    * 题目解析
//    * @property title - 解析标题
//    * @property content - 解析内容（数组形式支持多段落）
//    */
//   analysis: {
//     title: string
//     content: string[]
//   }
//   /** 关联的知识点列表 */
//   knowledgePoints: string[]
// }

/**
 * 处理单选题数据
 * @param data 原始流式数据，包含题目信息
 * @returns 处理后的单选题数据，符合ProcessedQuestionData接口格式
 */
function processSingleChoiceData(data: Question.QuestionData): Question.ProcessedQuestionData {
  // 解构原始数据并设置默认值
  const {
    QuestionType: typeText, // 题型文本描述
    QuestionTypeId: typeId, // 题型ID
    Title: title, // 题目内容
    Options = [], // 选项数组，默认为空数组
    Answer: correctAnswer, // 正确答案
    Analysis, // 答案解析
    KnowledgePoints = [], // 知识点数组，默认为空数组
  } = data.Question

  // 返回处理后的数据结构
  return {
    id: nanoid(), // 使用nanoid生成唯一ID
    componentsName: getQuestionComponentName(typeId), // 获取对应组件名称
    typeText, // 题型文本
    typeId, // 题型ID
    title, // 题目内容
    options: Options.map(option => ({ // 转换选项格式
      label: option.Content, // 选项显示文本
      value: option.Option, // 选项值
    })),
    correctAnswer, // 正确答案
    analysis: Analysis, // 答案解析
    knowledgePoints: KnowledgePoints || [], // 知识点数组
  }
}

/**
 * 处理多选题数据
 */
// function processMultipleChoiceData(data: StreamQuestionData): ProcessedQuestionData {
//   const options = data.Question.Options?.map(option => ({
//     label: option.Content,
//     value: option.Option,
//   })) || []

//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     options,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理判断题数据
 */
// function processTrueFalseData(data: StreamQuestionData): ProcessedQuestionData {
//   const options = [
//     { label: '正确', value: 'T' },
//     { label: '错误', value: 'F' },
//   ]

//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     options,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理填空题数据
 */
// function processFillBlankData(data: StreamQuestionData): ProcessedQuestionData {
//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理简答题数据
 */
// function processShortAnswerData(data: StreamQuestionData): ProcessedQuestionData {
//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 主要的数据处理函数
 * 根据题型ID处理不同类型的题目数据
 */
export function processQuestionData(data: Question.QuestionData): Question.ProcessedQuestionData | null {
  if (!data.Question) {
    return null
  }

  const questionTypeId = data.Question.QuestionTypeId
  switch (questionTypeId) {
    case '2': // 单选题
      return processSingleChoiceData(data)
    case '11': // 判断题
      return processSingleChoiceData(data)
    default:
      return processSingleChoiceData(data)
  }
  // switch (questionTypeId) {
  //   case '1': // 多选题
  //     // return processMultipleChoiceData(data)
  //   case '2': // 单选题
  //     return processSingleChoiceData(data)
  //   case '3': // 判断题
  //     // return processTrueFalseData(data)
  //   case '4': // 填空题
  //     // return processFillBlankData(data)
  //   case '5': // 简答题
  //     // return processShortAnswerData(data)
  //   case '6': // 论述题
  //     // return processShortAnswerData(data) // 论述题和简答题处理方式相同
  //   default:
  //     console.warn(`未知的题型ID: ${questionTypeId}`)
  //     return processSingleChoiceData(data) // 默认按单选题处理
  // }
}

/**
 * 获取题型对应的组件名称
 */
function getQuestionComponentName(typeId: string): string {
  const componentMap: Record<string, string> = {
    2: 'SingleChoice', // 单项选择题
    11: 'TrueFalse', // 判断题
    10: 'MultipleChoice', // 多项选择题
    4: 'FillBlank',

  }

  return componentMap[typeId] || 'SingleChoice'
}

/**
 * 将前端题目数据转换为API所需的GeneratedQuestion格式
 * @param question 前端处理后的题目数据
 * @returns 转换后的GeneratedQuestion格式数据
 */
export function convertToGeneratedQuestion(question: Question.ProcessedQuestionData): GeneratedQuestion {
  if (!question) {
    throw new Error('题目数据不能为空')
  }

  // 转换选项格式
  const options: GeneratedQuestionOption[] | undefined = question.options?.map(option => ({
    Content: option.label || '',
    Option: option.value || '',
  }))

  // 转换知识点格式（如果有的话）
  const knowledgePoints: GeneratedQuestionKnowledgePoint[] | undefined = question.knowledgePoints?.length > 0
    ? question.knowledgePoints.map((point, index) => ({
        Content: point || '',
        Id: `kp_${question.id}_${index}`, // 使用题目ID生成更唯一的知识点ID
        Level: 1, // 默认层级，实际使用时可能需要真实的层级信息
      }))
    : undefined

  return {
    Analysis: question.analysis || '',
    Answer: question.correctAnswer || '',
    Chapters: undefined, // 前端数据中没有章节信息，设为undefined
    KnowledgePoints: knowledgePoints,
    Options: options,
    QuestionType: question.typeText || '',
    QuestionTypeId: question.typeId || '',
    Title: question.title || '',
  }
}

/**
 * 批量转换题目数据
 * @param questions 前端题目数据数组
 * @returns 转换后的GeneratedQuestion格式数据数组
 */
export function convertQuestionsToGeneratedQuestions(questions: Question.ProcessedQuestionData[]): GeneratedQuestion[] {
  if (!Array.isArray(questions)) {
    throw new TypeError('题目数据必须是数组格式')
  }

  return questions.map(question => convertToGeneratedQuestion(question))
}
